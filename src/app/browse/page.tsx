'use client';

import React, { useEffect, useState, useCallback, useMemo, useRef, Suspense } from 'react';
import { useSearchParams, useRouter, usePathname } from 'next/navigation';
import { X } from 'lucide-react';
import ResourceCard from '@/components/resource/ResourceCard';
import SmartFilterSection from '@/components/browse/SmartFilterSection';
import ActiveFilters from '@/components/browse/ActiveFilters';
import ComprehensiveFilters from '@/components/browse/ComprehensiveFilters';
import { getEntities, getEntityTypes, getCategories, getTags, getFeatures } from '@/services/api';
import { Entity, EntityType, Category, Tag, Feature, PaginationMeta, GetEntitiesParams } from '@/types/entity';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { SearchIcon, Filter } from 'lucide-react';
import ResourceCardSkeleton from '@/components/resource/ResourceCardSkeleton';
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet";


const ITEMS_PER_PAGE = 9; // Adjusted for potentially larger cards or 3-column layout

const SORT_OPTIONS = [
  { value: 'name', label: 'Name' },
  { value: 'createdAt', label: 'Date Added' },
  { value: 'updatedAt', label: 'Last Updated' },
  { value: 'reviewCount', label: 'Review Count' },
  // 🎉 NOW WORKING! Backend supports all these!
  { value: 'averageRating', label: 'Rating' },
  { value: 'saveCount', label: 'Save Count' },
  { value: 'viewCount', label: 'View Count' },
  { value: 'popularity', label: 'Popularity' },
  // { value: 'relevance', label: 'Relevance' }, // Keep disabled if not implemented
  // { value: 'foundedYear', label: 'Founded Year' }, // Keep disabled if not implemented
];



function BrowsePageContent() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { session } = useAuth();

  const [entities, setEntities] = useState<Entity[]>([]);
  const [allEntityTypes, setAllEntityTypes] = useState<EntityType[]>([]); // To store all available entity types
  const [allCategories, setAllCategories] = useState<Category[]>([]); // New state for categories
  const [allTags, setAllTags] = useState<Tag[]>([]); // New state for tags
  const [allFeatures, setAllFeatures] = useState<Feature[]>([]); // New state for features
  const [paginationMeta, setPaginationMeta] = useState<PaginationMeta | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingFilters, setIsLoadingFilters] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isLoadingMore, setIsLoadingMore] = useState(false); // For infinite scroll
  const [showRedirectNotice, setShowRedirectNotice] = useState(false);



  // State for mobile filter sidebar visibility
  const [isMobileFilterOpen, setIsMobileFilterOpen] = useState(false);

  // Note: Filter counts removed - using clean filter display without counts
  // In the future, we could add a separate API endpoint to get filter counts if needed

  // Derived state from URL search parameters
  const searchTerm = useMemo(() => searchParams?.get('searchTerm') || '', [searchParams]);
  const selectedEntityTypeIds = useMemo(() => searchParams?.getAll('entityTypeIds') || [], [searchParams]);
  const selectedCategoryIds = useMemo(() => searchParams?.getAll('categoryIds') || [], [searchParams]); // New
  const selectedTagIds = useMemo(() => searchParams?.getAll('tagIds') || [], [searchParams]); // New
  const selectedFeatureIds = useMemo(() => searchParams?.getAll('featureIds') || [], [searchParams]); // New for features
  const currentPage = useMemo(() => parseInt(searchParams?.get('page') || '1', 10), [searchParams]);
  const sortBy = useMemo(() => {
    const sortParam = searchParams?.get('sortBy') || 'createdAt';
    // Ensure the sort parameter is valid - ALL WORKING NOW! 🎉
    const validSortOptions = ['createdAt', 'updatedAt', 'name', 'reviewCount', 'averageRating', 'saveCount', 'viewCount', 'popularity'] as const;
    return validSortOptions.includes(sortParam as any) ? sortParam as typeof validSortOptions[number] : 'createdAt';
  }, [searchParams]);
  const sortOrder = useMemo(() => (searchParams?.get('sortOrder') || 'desc') as 'asc' | 'desc', [searchParams]); // Default to desc for createdAt

  // Advanced filter state from URL
  const hasFreeTier = useMemo(() => searchParams?.get('hasFreeTier') === 'true' ? true : undefined, [searchParams]);
  const apiAccess = useMemo(() => searchParams?.get('apiAccess') === 'true' ? true : undefined, [searchParams]);
  const employeeCountRanges = useMemo(() => {
    // Always use getAll to ensure we get an array, even for single values
    const values = searchParams?.getAll('employeeCountRanges') || [];
    const validValues = values.filter(v => v && v.trim());
    return validValues.length > 0 ? validValues : undefined;
  }, [searchParams]);
  const fundingStages = useMemo(() => {
    const values = searchParams?.getAll('fundingStages') || [];
    const validValues = values.filter(v => v && v.trim());
    return validValues.length > 0 ? validValues : undefined;
  }, [searchParams]);
  const pricingModels = useMemo(() => {
    const values = searchParams?.getAll('pricingModels') || [];
    const validValues = values.filter(v => v && v.trim());
    return validValues.length > 0 ? validValues : undefined;
  }, [searchParams]);
  const priceRanges = useMemo(() => {
    const values = searchParams?.getAll('priceRanges') || [];
    const validValues = values.filter(v => v && v.trim());
    return validValues.length > 0 ? validValues : undefined;
  }, [searchParams]);
  const createdAtFrom = useMemo(() => searchParams?.get('createdAtFrom') || undefined, [searchParams]);
  const createdAtTo = useMemo(() => searchParams?.get('createdAtTo') || undefined, [searchParams]);
  const locationSearch = useMemo(() => searchParams?.get('locationSearch') || undefined, [searchParams]);

  // New filter state from URL
  const ratingMin = useMemo(() => {
    const value = searchParams?.get('rating_min');
    return value ? parseFloat(value) : undefined;
  }, [searchParams]);
  const ratingMax = useMemo(() => {
    const value = searchParams?.get('rating_max');
    return value ? parseFloat(value) : undefined;
  }, [searchParams]);
  const reviewCountMin = useMemo(() => {
    const value = searchParams?.get('review_count_min');
    return value ? parseInt(value, 10) : undefined;
  }, [searchParams]);
  const reviewCountMax = useMemo(() => {
    const value = searchParams?.get('review_count_max');
    return value ? parseInt(value, 10) : undefined;
  }, [searchParams]);
  const affiliateStatus = useMemo(() => {
    const value = searchParams?.get('affiliate_status');
    return value as 'NONE' | 'APPLIED' | 'APPROVED' | 'REJECTED' | undefined;
  }, [searchParams]);
  const hasAffiliateLink = useMemo(() => searchParams?.get('has_affiliate_link') === 'true' ? true : undefined, [searchParams]);

  // MISSING CRITICAL FILTERS - Platform & Integration
  const integrations = useMemo(() => {
    const values = searchParams?.getAll('integrations') || [];
    const validValues = values.filter(v => v && v.trim());
    return validValues.length > 0 ? validValues : undefined;
  }, [searchParams]);
  const platforms = useMemo(() => {
    const values = searchParams?.getAll('platforms') || [];
    const validValues = values.filter(v => v && v.trim());
    return validValues.length > 0 ? validValues : undefined;
  }, [searchParams]);
  const targetAudience = useMemo(() => {
    const values = searchParams?.getAll('targetAudience') || [];
    const validValues = values.filter(v => v && v.trim());
    return validValues.length > 0 ? validValues : undefined;
  }, [searchParams]);

  // Status & Moderation filters
  const status = useMemo(() => {
    const value = searchParams?.get('status');
    return value as 'PENDING' | 'ACTIVE' | 'REJECTED' | 'INACTIVE' | 'ARCHIVED' | 'NEEDS_REVISION' | undefined;
  }, [searchParams]);
  const submitterId = useMemo(() => searchParams?.get('submitterId') || undefined, [searchParams]);

  // Entity-specific filters (flat structure)
  // Tool/AI Tool filters
  const technicalLevels = useMemo(() => searchParams?.get('technical_levels')?.split(',').filter(Boolean) || [], [searchParams]);
  const learningCurves = useMemo(() => searchParams?.get('learning_curves')?.split(',').filter(Boolean) || [], [searchParams]);
  const hasApi = useMemo(() => {
    const value = searchParams?.get('has_api');
    return value === 'true' ? true : value === 'false' ? false : undefined;
  }, [searchParams]);
  const openSource = useMemo(() => {
    const value = searchParams?.get('open_source');
    return value === 'true' ? true : value === 'false' ? false : undefined;
  }, [searchParams]);
  const mobileSupport = useMemo(() => {
    const value = searchParams?.get('mobile_support');
    return value === 'true' ? true : value === 'false' ? false : undefined;
  }, [searchParams]);
  const demoAvailable = useMemo(() => {
    const value = searchParams?.get('demo_available');
    return value === 'true' ? true : value === 'false' ? false : undefined;
  }, [searchParams]);

  // Course filters
  const skillLevels = useMemo(() => searchParams?.get('skill_levels')?.split(',').filter(Boolean) || [], [searchParams]);
  const certificateAvailable = useMemo(() => {
    const value = searchParams?.get('certificate_available');
    return value === 'true' ? true : value === 'false' ? false : undefined;
  }, [searchParams]);
  const instructorName = useMemo(() => searchParams?.get('instructor_name') || undefined, [searchParams]);
  const durationText = useMemo(() => searchParams?.get('duration_text') || undefined, [searchParams]);

  // Job filters
  const employmentTypes = useMemo(() => searchParams?.get('employment_types')?.split(',').filter(Boolean) || [], [searchParams]);
  const experienceLevels = useMemo(() => searchParams?.get('experience_levels')?.split(',').filter(Boolean) || [], [searchParams]);
  const remoteWork = useMemo(() => {
    const value = searchParams?.get('remote_work');
    return value === 'true' ? true : value === 'false' ? false : undefined;
  }, [searchParams]);

  // Hardware filters
  const hardwareTypes = useMemo(() => searchParams?.get('hardware_types')?.split(',').filter(Boolean) || [], [searchParams]);
  const manufacturers = useMemo(() => searchParams?.get('manufacturers')?.split(',').filter(Boolean) || [], [searchParams]);
  const hasDatasheet = useMemo(() => {
    const value = searchParams?.get('has_datasheet');
    return value === 'true' ? true : value === 'false' ? false : undefined;
  }, [searchParams]);

  // Event filters
  const eventTypes = useMemo(() => searchParams?.get('event_types')?.split(',').filter(Boolean) || [], [searchParams]);
  const isOnline = useMemo(() => {
    const value = searchParams?.get('is_online');
    return value === 'true' ? true : value === 'false' ? false : undefined;
  }, [searchParams]);
  const registrationRequired = useMemo(() => {
    const value = searchParams?.get('registration_required');
    return value === 'true' ? true : value === 'false' ? false : undefined;
  }, [searchParams]);

  // Local state for the search input field for better UX (e.g., debounce or submit on enter)
  const [localSearchInput, setLocalSearchInput] = useState(searchTerm);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Ref for the element that triggers loading more items
  const loadMoreRef = useRef<HTMLDivElement | null>(null);

  // Effect to update local search input when URL searchTerm changes (e.g., back/forward navigation)
  useEffect(() => {
    setLocalSearchInput(searchTerm);
  }, [searchTerm]);

  // Check for redirect from old URL
  useEffect(() => {
    if (searchParams?.get('redirected') === 'true' && searchParams?.get('reason') === 'old-url') {
      setShowRedirectNotice(true);
      // Clean up URL parameters
      const newParams = new URLSearchParams(searchParams);
      newParams.delete('redirected');
      newParams.delete('reason');
      router.replace(`${pathname}?${newParams.toString()}`, { scroll: false });
    }
  }, [searchParams, pathname, router]);

  const fetchFilterData = useCallback(async () => {
    setIsLoadingFilters(true);
    try {
      const [typesResult, categoriesResult, tagsResult, featuresResult] = await Promise.all([
        allEntityTypes.length === 0 ? getEntityTypes(session?.access_token) : Promise.resolve(allEntityTypes),
        allCategories.length === 0 ? getCategories(session?.access_token) : Promise.resolve(allCategories),
        allTags.length === 0 ? getTags(session?.access_token) : Promise.resolve(allTags),
        allFeatures.length === 0 ? getFeatures(session?.access_token) : Promise.resolve(allFeatures),
      ]);

      if (allEntityTypes.length === 0 && Array.isArray(typesResult)) setAllEntityTypes(typesResult as EntityType[]);
      if (allCategories.length === 0 && Array.isArray(categoriesResult)) setAllCategories(categoriesResult as Category[]);
      if (allTags.length === 0 && Array.isArray(tagsResult)) setAllTags(tagsResult as Tag[]);
      if (allFeatures.length === 0 && Array.isArray(featuresResult)) setAllFeatures(featuresResult as Feature[]);
    } catch (err: unknown) {
      console.error('Failed to fetch filter data:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to load filter options';
      setError(`Filter Data API: ${errorMessage}`);
      // Not setting main error state here, as entity fetching might still work
    }
    setIsLoadingFilters(false);
  }, [session, allEntityTypes, allCategories, allTags, allFeatures]);

  const fetchEntitiesData = useCallback(async (isLoadMore = false) => {
    if (!isLoadMore) {
      setIsLoading(true);
      setEntities([]); // Clear entities for new filter/search, but not for load more
    } else {
      setIsLoadingMore(true);
    }
    setError(null);

    // Determine page to fetch
    const pageToFetch = isLoadMore && paginationMeta ? paginationMeta.page + 1 : currentPage;

    try {
      const params: GetEntitiesParams = {
        page: pageToFetch,
        limit: ITEMS_PER_PAGE,
        ...(searchTerm && { searchTerm }),
        ...(selectedEntityTypeIds.length > 0 && { entityTypeIds: selectedEntityTypeIds }),
        ...(selectedCategoryIds.length > 0 && { categoryIds: selectedCategoryIds }),
        ...(selectedTagIds.length > 0 && { tagIds: selectedTagIds }),
        ...(selectedFeatureIds.length > 0 && { featureIds: selectedFeatureIds }),
        sortBy: sortBy,
        sortOrder: sortOrder,
        // Advanced filters
        ...(hasFreeTier !== undefined && { hasFreeTier }),
        ...(apiAccess !== undefined && { apiAccess }),
        ...(employeeCountRanges && employeeCountRanges.length > 0 && { employeeCountRanges }),
        ...(fundingStages && fundingStages.length > 0 && { fundingStages }),
        ...(pricingModels && pricingModels.length > 0 && { pricingModels }),
        ...(priceRanges && priceRanges.length > 0 && { priceRanges }),
        ...(createdAtFrom && { createdAtFrom }),
        ...(createdAtTo && { createdAtTo }),
        ...(locationSearch && { locationSearch }),
        // New filters
        ...(ratingMin !== undefined && { rating_min: ratingMin }),
        ...(ratingMax !== undefined && { rating_max: ratingMax }),
        ...(reviewCountMin !== undefined && { review_count_min: reviewCountMin }),
        ...(reviewCountMax !== undefined && { review_count_max: reviewCountMax }),
        ...(affiliateStatus && { affiliate_status: affiliateStatus }),
        ...(hasAffiliateLink !== undefined && { has_affiliate_link: hasAffiliateLink }),
        // MISSING CRITICAL FILTERS
        ...(integrations && integrations.length > 0 && { integrations }),
        ...(platforms && platforms.length > 0 && { platforms }),
        ...(targetAudience && targetAudience.length > 0 && { targetAudience }),
        ...(status && { status }),
        ...(submitterId && { submitterId }),
      };

      // Add entity_type_filters if present in URL
      const entityTypeFiltersParam = searchParams.get('entity_type_filters');
      if (entityTypeFiltersParam) {
        try {
          params.entity_type_filters = JSON.parse(entityTypeFiltersParam);
        } catch (e) {
          console.warn('Failed to parse entity_type_filters from URL:', e);
        }
      }



      const entitiesResponse = await getEntities(params, session?.access_token);
      
      setEntities(prevEntities => 
        isLoadMore ? [...prevEntities, ...entitiesResponse.data] : entitiesResponse.data
      );
      setPaginationMeta(entitiesResponse.meta);

    } catch (err: unknown) {
      console.error('Failed to fetch entities:', err);
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred while fetching data.';
      setError(`Entities API: ${errorMessage}`);
      setEntities([]);
      setPaginationMeta(null);
    }
    if (!isLoadMore) {
      setIsLoading(false);
    } else {
      setIsLoadingMore(false);
    }
  }, [
    currentPage, // Still needed for initial load based on URL
    searchTerm,
    selectedEntityTypeIds,
    selectedCategoryIds,
    selectedTagIds,
    selectedFeatureIds, // Added
    sortBy,
    sortOrder,
    session,
    paginationMeta,
    // Advanced filters
    hasFreeTier,
    apiAccess,
    employeeCountRanges,
    fundingStages,
    pricingModels,
    priceRanges,
    createdAtFrom,
    createdAtTo,
    locationSearch,
    // New filters
    ratingMin,
    ratingMax,
    reviewCountMin,
    reviewCountMax,
    affiliateStatus,
    hasAffiliateLink,
    // MISSING CRITICAL FILTERS
    integrations,
    platforms,
    targetAudience,
    status,
    submitterId,
    // Entity-specific filters via searchParams
    searchParams,
  ]);

  useEffect(() => {
    fetchFilterData();
  }, [fetchFilterData]);

  useEffect(() => {
    if (!isLoadingFilters) { // Fetch entities only after filter data attempts to load
        fetchEntitiesData();
    }
  }, [fetchEntitiesData, isLoadingFilters]);

  // Setup IntersectionObserver
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && paginationMeta?.hasNextPage && !isLoading && !isLoadingMore) {
          // console.log('Reached bottom, loading more...');
          fetchEntitiesData(true); // Pass true for loadMore
        }
      },
      { threshold: 1.0 } // Trigger when 100% of the target is visible
    );

    const currentLoadMoreRef = loadMoreRef.current;
    if (currentLoadMoreRef) {
      observer.observe(currentLoadMoreRef);
    }

    return () => {
      if (currentLoadMoreRef) {
        observer.unobserve(currentLoadMoreRef);
      }
    };
  }, [fetchEntitiesData, paginationMeta, isLoading, isLoadingMore]); // Add dependencies

  // Function to update URL query parameters
  const updateQueryAndNavigate = (newQueryValues: Record<string, string | string[] | null>) => {
    const current = new URLSearchParams(Array.from(searchParams?.entries() || []));
    let resetPage = false;

    // Define which parameters should always be treated as arrays
    const arrayParameters = new Set([
      'entityTypeIds', 'categoryIds', 'tagIds', 'featureIds',
      'employeeCountRanges', 'fundingStages', 'pricingModels', 'priceRanges',
      'integrations', 'platforms', 'targetAudience'
    ]);

    Object.entries(newQueryValues).forEach(([key, value]) => {
      current.delete(key); // Always delete first to handle array values correctly
      if (value !== null && value !== '' && (!Array.isArray(value) || value.length > 0)) {
        if (Array.isArray(value)) {
          value.forEach(v => current.append(key, v));
        } else if (arrayParameters.has(key)) {
          // For array parameters, always use append even for single values
          current.append(key, value as string);
        } else {
          current.set(key, value as string);
        }
      }
      // Reset page if any filter or search term changes, but not if only page itself changes
      if (key !== 'page' && !(key === 'sortBy' && newQueryValues['sortBy'] === sortBy) && !(key === 'sortOrder' && newQueryValues['sortOrder'] === sortOrder)) {
        resetPage = true;
      }
    });

    if (resetPage) {
      current.set('page', '1');
    }

    router.push(`${pathname}?${current.toString()}`, { scroll: false });
  };

  const handleSearchSubmit = (e?: React.FormEvent<HTMLFormElement>) => {
    e?.preventDefault();
    updateQueryAndNavigate({ searchTerm: localSearchInput });
  };

  // Debounced search handler for better performance
  const handleDebouncedSearch = useCallback((value: string) => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    debounceTimeoutRef.current = setTimeout(() => {
      if (value.trim() !== searchTerm) {
        updateQueryAndNavigate({ searchTerm: value.trim() || null });
      }
    }, 500); // 500ms debounce
  }, [searchTerm, updateQueryAndNavigate]);

  const handleEntityTypeToggle = (typeId: string) => {
    const newSelectedIds = selectedEntityTypeIds.includes(typeId)
      ? selectedEntityTypeIds.filter(id => id !== typeId)
      : [...selectedEntityTypeIds, typeId];
    updateQueryAndNavigate({ entityTypeIds: newSelectedIds });
  };

  const handleCategoryToggle = (categoryId: string) => { // New handler
    const newSelectedIds = selectedCategoryIds.includes(categoryId)
      ? selectedCategoryIds.filter(id => id !== categoryId)
      : [...selectedCategoryIds, categoryId];
    updateQueryAndNavigate({ categoryIds: newSelectedIds });
  };

  const handleTagToggle = (tagId: string) => {
    const newSelectedIds = selectedTagIds.includes(tagId)
      ? selectedTagIds.filter(id => id !== tagId)
      : [...selectedTagIds, tagId];
    updateQueryAndNavigate({ tagIds: newSelectedIds });
  };

  const handleFeatureToggle = (featureId: string) => {
    const newSelectedIds = selectedFeatureIds.includes(featureId)
      ? selectedFeatureIds.filter(id => id !== featureId)
      : [...selectedFeatureIds, featureId];
    updateQueryAndNavigate({ featureIds: newSelectedIds });
  };

  const handleAdvancedFilterChange = (filterName: string, value: boolean | string | string[] | number | object | null) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 [Browse] Filter change:', filterName, value);
    }

    // Check if this is an entity-specific filter
    const entitySpecificFilters = [
      'technical_levels', 'learning_curves', 'has_api', 'has_free_tier', 'open_source', 'mobile_support', 'demo_available',
      'skill_levels', 'certificate_available', 'instructor_name', 'duration_text', 'enrollment_min', 'enrollment_max',
      'employment_types', 'experience_levels', 'salary_min', 'salary_max', 'remote_work',
      'hardware_types', 'manufacturers', 'has_datasheet',
      'event_types', 'is_online', 'registration_required'
    ];

    if (entitySpecificFilters.includes(filterName)) {
      // Handle entity-specific filters using entity_type_filters format
      handleEntitySpecificFilterChange(filterName, value);
    } else {
      // Handle regular filters
      let urlValue: string | string[] | null = null;

      if (value === null) {
        urlValue = null;
      } else if (typeof value === 'boolean') {
        urlValue = value ? 'true' : null;
      } else if (typeof value === 'number') {
        urlValue = value.toString();
      } else if (Array.isArray(value)) {
        urlValue = value.length > 0 ? value : null;
      } else if (typeof value === 'object') {
        // For objects, JSON stringify if they have content
        urlValue = Object.keys(value).length > 0 ? JSON.stringify(value) : null;
      } else {
        urlValue = value || null;
      }
      updateQueryAndNavigate({ [filterName]: urlValue });
    }
  };

  const handleEntitySpecificFilterChange = (filterName: string, value: any) => {
    // Build entity_type_filters object based on selected entity types and current filters
    const currentEntityTypeFilters = searchParams.get('entity_type_filters');
    let entityFilters: Record<string, any> = {};

    try {
      if (currentEntityTypeFilters) {
        entityFilters = JSON.parse(currentEntityTypeFilters);
      }
    } catch (e) {
      console.warn('Failed to parse existing entity_type_filters:', e);
      entityFilters = {};
    }

    // Map entity type IDs to backend keys using the utility
    const selectedEntityTypeNames = selectedEntityTypeIds
      .map(id => allEntityTypes.find(type => type.id === id)?.name)
      .filter(Boolean) as string[];

    // Update filters for each selected entity type
    selectedEntityTypeNames.forEach(entityTypeName => {
      // Convert display name to backend key (e.g., "AI Tool" -> "tool")
      const backendKey = getBackendKeyForEntityType(entityTypeName);
      if (backendKey) {
        if (!entityFilters[backendKey]) {
          entityFilters[backendKey] = {};
        }

        if (value === null || value === undefined || (Array.isArray(value) && value.length === 0)) {
          // Remove the filter
          delete entityFilters[backendKey][filterName];
          // Remove the entity type if no filters remain
          if (Object.keys(entityFilters[backendKey]).length === 0) {
            delete entityFilters[backendKey];
          }
        } else {
          // Set the filter
          entityFilters[backendKey][filterName] = value;
        }
      }
    });

    // Update URL with the new entity_type_filters
    const entityFiltersString = Object.keys(entityFilters).length > 0 ? JSON.stringify(entityFilters) : null;
    updateQueryAndNavigate({ entity_type_filters: entityFiltersString });
  };

  // Helper function to map entity type display names to backend keys
  const getBackendKeyForEntityType = (displayName: string): string | null => {
    const mapping: Record<string, string> = {
      'AI Tool': 'tool',
      'Course': 'course',
      'Job': 'job',
      'Hardware': 'hardware',
      'Event': 'event',
      'Agency': 'agency',
      'Software': 'software',
      'Research Paper': 'research_paper',
      'Podcast': 'podcast',
      'Community': 'community',
      'Grant': 'grant',
      'Newsletter': 'newsletter',
      'Book': 'book'
    };
    return mapping[displayName] || null;
  };

  const handleClearAdvancedFilters = () => {
    updateQueryAndNavigate({
      hasFreeTier: null,
      apiAccess: null,
      employeeCountRanges: null,
      fundingStages: null,
      pricingModels: null,
      priceRanges: null,
      createdAtFrom: null,
      createdAtTo: null,
      locationSearch: null,
      rating_min: null,
      rating_max: null,
      review_count_min: null,
      review_count_max: null,
      affiliate_status: null,
      has_affiliate_link: null,
      // MISSING CRITICAL FILTERS
      integrations: null,
      platforms: null,
      targetAudience: null,
      status: null,
      submitterId: null,
      // Entity-specific filters
      entity_type_filters: null,
    });
  };

  const handleClearAllFilters = () => {
    setLocalSearchInput(''); // Also clear local search input
    updateQueryAndNavigate({
      searchTerm: null,
      entityTypeIds: null,
      categoryIds: null,
      tagIds: null,
      featureIds: null, // New
      // Advanced filters
      hasFreeTier: null,
      apiAccess: null,
      employeeCountRanges: null,
      fundingStages: null,
      pricingModels: null,
      priceRanges: null,
      createdAtFrom: null,
      createdAtTo: null,
      locationSearch: null,
      rating_min: null,
      rating_max: null,
      review_count_min: null,
      review_count_max: null,
      affiliate_status: null,
      has_affiliate_link: null,
      integrations: null,
      platforms: null,
      targetAudience: null,
      status: null,
      submitterId: null,
      // Entity-specific filters
      entity_type_filters: null,
      page: '1'
    });
  };

  const handleSortByChange = (newSortBy: string) => {
    updateQueryAndNavigate({ sortBy: newSortBy });
  };



  const getPillName = useCallback((id: string, source: 'entityType' | 'category' | 'tag' | 'feature'): string => {
    if (source === 'entityType') return allEntityTypes.find(t => t.id === id)?.name || id;
    if (source === 'category') return allCategories.find(c => c.id === id)?.name || id;
    if (source === 'tag') return allTags.find(t => t.id === id)?.name || id;
    if (source === 'feature') return allFeatures.find(f => f.id === id)?.name || id;
    return id;
  }, [allEntityTypes, allCategories, allTags, allFeatures]);

  // Generate advanced filter pills
  const advancedFilterPills = useMemo(() => {
    const pills: Array<{ key: string; name: string; type: 'advanced' }> = [];

    if (hasFreeTier) pills.push({ key: 'hasFreeTier', name: 'Has Free Tier', type: 'advanced' });
    if (apiAccess) pills.push({ key: 'apiAccess', name: 'API Access', type: 'advanced' });
    if (ratingMin !== undefined) pills.push({ key: 'rating_min', name: `Rating ≥ ${ratingMin}`, type: 'advanced' });
    if (ratingMax !== undefined) pills.push({ key: 'rating_max', name: `Rating ≤ ${ratingMax}`, type: 'advanced' });
    if (reviewCountMin !== undefined) pills.push({ key: 'review_count_min', name: `Reviews ≥ ${reviewCountMin}`, type: 'advanced' });
    if (reviewCountMax !== undefined) pills.push({ key: 'review_count_max', name: `Reviews ≤ ${reviewCountMax}`, type: 'advanced' });
    if (affiliateStatus) pills.push({ key: 'affiliate_status', name: `Affiliate: ${affiliateStatus}`, type: 'advanced' });
    if (hasAffiliateLink) pills.push({ key: 'has_affiliate_link', name: 'Has Affiliate Link', type: 'advanced' });
    if (locationSearch) pills.push({ key: 'locationSearch', name: `Location: ${locationSearch}`, type: 'advanced' });
    if (createdAtFrom) pills.push({ key: 'createdAtFrom', name: `From: ${createdAtFrom}`, type: 'advanced' });
    if (createdAtTo) pills.push({ key: 'createdAtTo', name: `To: ${createdAtTo}`, type: 'advanced' });

    // Array filters
    if (employeeCountRanges && employeeCountRanges.length > 0) {
      pills.push({ key: 'employeeCountRanges', name: `Employee Count (${employeeCountRanges.length})`, type: 'advanced' });
    }
    if (fundingStages && fundingStages.length > 0) {
      pills.push({ key: 'fundingStages', name: `Funding (${fundingStages.length})`, type: 'advanced' });
    }
    if (pricingModels && pricingModels.length > 0) {
      pills.push({ key: 'pricingModels', name: `Pricing (${pricingModels.length})`, type: 'advanced' });
    }
    if (priceRanges && priceRanges.length > 0) {
      pills.push({ key: 'priceRanges', name: `Price Range (${priceRanges.length})`, type: 'advanced' });
    }

    // MISSING CRITICAL FILTERS
    if (integrations && integrations.length > 0) {
      pills.push({ key: 'integrations', name: `Integrations (${integrations.length})`, type: 'advanced' });
    }
    if (platforms && platforms.length > 0) {
      pills.push({ key: 'platforms', name: `Platforms (${platforms.length})`, type: 'advanced' });
    }
    if (targetAudience && targetAudience.length > 0) {
      pills.push({ key: 'targetAudience', name: `Audience (${targetAudience.length})`, type: 'advanced' });
    }
    if (status) pills.push({ key: 'status', name: `Status: ${status}`, type: 'advanced' });
    if (submitterId) pills.push({ key: 'submitterId', name: 'Specific Submitter', type: 'advanced' });

    // Entity-specific filters
    const entitySpecificFiltersCount = [
      technicalLevels.length > 0 ? 1 : 0,
      learningCurves.length > 0 ? 1 : 0,
      hasApi !== undefined ? 1 : 0,
      openSource !== undefined ? 1 : 0,
      mobileSupport !== undefined ? 1 : 0,
      demoAvailable !== undefined ? 1 : 0,
      skillLevels.length > 0 ? 1 : 0,
      certificateAvailable !== undefined ? 1 : 0,
      instructorName ? 1 : 0,
      durationText ? 1 : 0,
      employmentTypes.length > 0 ? 1 : 0,
      experienceLevels.length > 0 ? 1 : 0,
      remoteWork !== undefined ? 1 : 0,
      hardwareTypes.length > 0 ? 1 : 0,
      manufacturers.length > 0 ? 1 : 0,
      hasDatasheet !== undefined ? 1 : 0,
      eventTypes.length > 0 ? 1 : 0,
      isOnline !== undefined ? 1 : 0,
      registrationRequired !== undefined ? 1 : 0,
    ].reduce((total, count) => total + count, 0);

    if (entitySpecificFiltersCount > 0) {
      pills.push({ key: 'entity_specific_filters', name: `Type-Specific (${entitySpecificFiltersCount})`, type: 'advanced' });
    }

    return pills;
  }, [hasFreeTier, apiAccess, ratingMin, ratingMax, reviewCountMin, reviewCountMax, affiliateStatus, hasAffiliateLink, locationSearch, createdAtFrom, createdAtTo, employeeCountRanges, fundingStages, pricingModels, priceRanges, integrations, platforms, targetAudience, status, submitterId, technicalLevels, learningCurves, hasApi, openSource, mobileSupport, demoAvailable, skillLevels, certificateAvailable, instructorName, durationText, employmentTypes, experienceLevels, remoteWork, hardwareTypes, manufacturers, hasDatasheet, eventTypes, isOnline, registrationRequired]);

  const handleRemoveAdvancedFilter = (key: string) => {
    updateQueryAndNavigate({ [key]: null });
  };

  const activeFiltersForPills = useMemo(() => {
    return [
      ...selectedEntityTypeIds.map(id => ({ id, name: getPillName(id, 'entityType'), type: 'entityTypeIds' as const })),
      ...selectedCategoryIds.map(id => ({ id, name: getPillName(id, 'category'), type: 'categoryIds' as const })),
      ...selectedTagIds.map(id => ({ id, name: getPillName(id, 'tag'), type: 'tagIds' as const })),
      ...selectedFeatureIds.map(id => ({ id, name: getPillName(id, 'feature'), type: 'featureIds' as const })),
    ];
  }, [selectedEntityTypeIds, selectedCategoryIds, selectedTagIds, selectedFeatureIds, getPillName]);

  const handleRemovePill = (id: string, type: 'entityTypeIds' | 'categoryIds' | 'tagIds' | 'featureIds') => {
    if (type === 'entityTypeIds') handleEntityTypeToggle(id);
    if (type === 'categoryIds') handleCategoryToggle(id);
    if (type === 'tagIds') handleTagToggle(id);
    if (type === 'featureIds') handleFeatureToggle(id);
  };



  // Remove early returns - handle all states within the main layout

  return (
    <div className="min-h-screen bg-gray-50" data-testid="browse-page">
      {/* Redirect Notice Banner */}
      {showRedirectNotice && (
        <div className="bg-blue-50 border-b border-blue-200">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="text-blue-800 text-sm">
                  <strong>Notice:</strong> You were redirected from an old URL. Our entity pages now use SEO-friendly URLs for better discoverability.
                </div>
              </div>
              <button
                onClick={() => setShowRedirectNotice(false)}
                className="text-blue-600 hover:text-blue-800 transition-colors"
                aria-label="Dismiss notice"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Hero Section */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Discover AI Tools & Resources
            </h1>
            <p className="text-xl text-gray-600">
              Explore the latest AI tools, platforms, and resources to supercharge your workflow
            </p>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">

        {/* Mobile Filter Toggle */}
        <div className="lg:hidden mb-6">
          <Sheet open={isMobileFilterOpen} onOpenChange={setIsMobileFilterOpen}>
            <SheetTrigger asChild>
              <Button variant="outline" className="inline-flex items-center gap-2">
                <Filter className="h-4 w-4" />
                Filters
                {(selectedEntityTypeIds.length + selectedCategoryIds.length + selectedTagIds.length + selectedFeatureIds.length) > 0 && (
                  <Badge variant="secondary" className="ml-1">
                    {selectedEntityTypeIds.length + selectedCategoryIds.length + selectedTagIds.length + selectedFeatureIds.length}
                  </Badge>
                )}
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-[320px] overflow-y-auto">
              <div className="space-y-6 pt-6">
                <SmartFilterSection
                  title="Resource Type"
                  items={allEntityTypes}
                  selectedIds={selectedEntityTypeIds}
                  onToggle={handleEntityTypeToggle}
                  isLoading={isLoadingFilters}
                  defaultExpanded={true}
                />

                <SmartFilterSection
                  title="Category"
                  items={allCategories}
                  selectedIds={selectedCategoryIds}
                  onToggle={handleCategoryToggle}
                  isLoading={isLoadingFilters}
                />

                <SmartFilterSection
                  title="Features"
                  items={allFeatures}
                  selectedIds={selectedFeatureIds}
                  onToggle={handleFeatureToggle}
                  isLoading={isLoadingFilters}
                />

                <SmartFilterSection
                  title="Tags"
                  items={allTags}
                  selectedIds={selectedTagIds}
                  onToggle={handleTagToggle}
                  isLoading={isLoadingFilters}
                />

                <ComprehensiveFilters
                  hasFreeTier={hasFreeTier}
                  apiAccess={apiAccess}
                  employeeCountRanges={employeeCountRanges}
                  fundingStages={fundingStages}
                  pricingModels={pricingModels}
                  priceRanges={priceRanges}
                  createdAtFrom={createdAtFrom}
                  createdAtTo={createdAtTo}
                  locationSearch={locationSearch}
                  ratingMin={ratingMin}
                  ratingMax={ratingMax}
                  reviewCountMin={reviewCountMin}
                  reviewCountMax={reviewCountMax}
                  affiliateStatus={affiliateStatus}
                  hasAffiliateLink={hasAffiliateLink}
                  integrations={integrations}
                  platforms={platforms}
                  targetAudience={targetAudience}
                  status={status}
                  selectedEntityTypes={selectedEntityTypeIds}
                  allEntityTypes={allEntityTypes}
                  isLoading={isLoadingFilters}
                  onFilterChange={handleAdvancedFilterChange}
                  onClearAdvanced={handleClearAdvancedFilters}
                />
              </div>
            </SheetContent>
          </Sheet>
        </div>

        <div className="lg:flex lg:gap-8">
          {/* Desktop Filter Sidebar */}
          <aside className="hidden lg:block w-80 flex-shrink-0">
            <div className="sticky top-8 bg-white rounded-xl shadow-sm border border-gray-100 p-6 space-y-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Filters</h2>

              <SmartFilterSection
                title="Resource Type"
                items={allEntityTypes}
                selectedIds={selectedEntityTypeIds}
                onToggle={handleEntityTypeToggle}
                isLoading={isLoadingFilters}
                defaultExpanded={true}
              />

              <SmartFilterSection
                title="Category"
                items={allCategories}
                selectedIds={selectedCategoryIds}
                onToggle={handleCategoryToggle}
                isLoading={isLoadingFilters}
              />

              <SmartFilterSection
                title="Features"
                items={allFeatures}
                selectedIds={selectedFeatureIds}
                onToggle={handleFeatureToggle}
                isLoading={isLoadingFilters}
              />

              <SmartFilterSection
                title="Tags"
                items={allTags}
                selectedIds={selectedTagIds}
                onToggle={handleTagToggle}
                isLoading={isLoadingFilters}
              />

              <ComprehensiveFilters
                hasFreeTier={hasFreeTier}
                apiAccess={apiAccess}
                employeeCountRanges={employeeCountRanges}
                fundingStages={fundingStages}
                pricingModels={pricingModels}
                priceRanges={priceRanges}
                createdAtFrom={createdAtFrom}
                createdAtTo={createdAtTo}
                locationSearch={locationSearch}
                ratingMin={ratingMin}
                ratingMax={ratingMax}
                reviewCountMin={reviewCountMin}
                reviewCountMax={reviewCountMax}
                affiliateStatus={affiliateStatus}
                hasAffiliateLink={hasAffiliateLink}
                integrations={integrations}
                platforms={platforms}
                targetAudience={targetAudience}
                status={status}
                selectedEntityTypes={selectedEntityTypeIds}
                allEntityTypes={allEntityTypes}
                isLoading={isLoadingFilters}
                onFilterChange={handleAdvancedFilterChange}
                onClearAdvanced={handleClearAdvancedFilters}
              />
            </div>
          </aside>

          {/* Main Content Area */}
          <main className="flex-1 space-y-6">
            {/* Search Section */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <form onSubmit={handleSearchSubmit} className="space-y-4">
                <div className="relative">
                  <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <Input
                    id="search-input"
                    type="search"
                    value={localSearchInput}
                    onChange={(e) => {
                      setLocalSearchInput(e.target.value);
                      handleDebouncedSearch(e.target.value);
                    }}
                    placeholder="Search AI tools, platforms, and resources..."
                    className="pl-10 h-12 text-base border-gray-200 focus:border-indigo-500 focus:ring-indigo-500"
                  />
                </div>

                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                  <div className="text-sm text-gray-600">
                    {paginationMeta && paginationMeta.totalItems ? (
                      <span>
                        <span className="font-medium text-gray-900">{paginationMeta.totalItems}</span> resources found
                      </span>
                    ) : (
                      'Loading...'
                    )}
                  </div>

                  <Select value={sortBy} onValueChange={handleSortByChange}>
                    <SelectTrigger className="w-full sm:w-auto min-w-[200px] border-gray-200">
                      <SelectValue placeholder="Sort by..." />
                    </SelectTrigger>
                    <SelectContent>
                      {SORT_OPTIONS.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </form>
            </div>

            {/* Active Filter Pills */}
            <ActiveFilters
              filters={activeFiltersForPills}
              onRemove={handleRemovePill}
              onClearAll={handleClearAllFilters}
              searchTerm={searchTerm}
              onClearSearch={() => {
                setLocalSearchInput('');
                updateQueryAndNavigate({ searchTerm: null });
              }}
              advancedFilters={advancedFilterPills}
              onRemoveAdvanced={handleRemoveAdvancedFilter}
            />


            {/* Content Display Area */}
            {isLoading && entities.length === 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                {[...Array(ITEMS_PER_PAGE)].map((_, index) => (
                  <ResourceCardSkeleton key={`skeleton-${index}`} />
                ))}
              </div>
            ) : error ? (
              <div className="text-center py-10 bg-red-50 dark:bg-red-900/30 p-6 rounded-lg shadow">
                <p className="text-lg text-red-600 dark:text-red-400 mb-3">Error: {error}</p>
                <Button onClick={() => { fetchFilterData(); fetchEntitiesData();}} variant="destructive">
                  Try Again
                </Button>
              </div>
            ) : null}

            {/* Results Section */}
            {!isLoading && !error && entities.length === 0 ? (
              <div className="text-center py-16">
                <div className="max-w-md mx-auto">
                  <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                    <SearchIcon className="w-8 h-8 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No resources found
                  </h3>
                  <p className="text-gray-600 mb-6">
                    We couldn&apos;t find any resources matching your criteria. Try adjusting your search or filters above.
                  </p>

                  {/* Helpful suggestions */}
                  <div className="bg-gray-50 rounded-lg p-4 mb-6 text-left">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Try these suggestions:</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• Check your spelling or try different keywords</li>
                      <li>• Remove some filters to broaden your search</li>
                      <li>• Try searching for broader terms (e.g., &quot;AI&quot; instead of &quot;machine learning&quot;)</li>
                      <li>• Browse all resources by clearing filters</li>
                    </ul>
                  </div>

                  <div className="space-y-3">
                    <Button
                      variant="outline"
                      onClick={handleClearAllFilters}
                      className="text-sm"
                    >
                      Clear all filters & search
                    </Button>
                    <p className="text-xs text-gray-500">
                      Search and filters remain active above for easy modification
                    </p>
                  </div>
                </div>
              </div>
            ) : !isLoading && !error && entities.length > 0 ? (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                  {entities.map((entity) => (
                    <ResourceCard
                      key={`entity-${entity.id}`}
                      entity={entity}
                    />
                  ))}
                </div>

                {/* Load More Trigger / Indicator */}
                {paginationMeta?.hasNextPage && (
                  <div ref={loadMoreRef} className="text-center py-10">
                    {isLoadingMore ? (
                      <p className="text-lg text-gray-500 dark:text-gray-400">Loading more resources...</p>
                      // Optionally add a spinner here
                    ) : (
                      // You can have a button here if you prefer a manual "Load More" action initially
                      // <Button onClick={() => fetchEntitiesData(true)}>Load More</Button>
                      <p className="text-sm text-gray-400 dark:text-gray-500">Scroll down to load more.</p>
                    )}
                  </div>
                )}
              </>
            ) : null}
          </main>
        </div>
      </div>
      {/* Overlay for mobile when filter is open */}
      {isMobileFilterOpen && (
        <div 
          className="fixed inset-0 z-30 bg-black/30 lg:hidden"
          onClick={() => setIsMobileFilterOpen(false)}
        ></div>
      )}
    </div>
  );
}

export default function BrowsePage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <div className="bg-white border-b border-gray-200">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="text-center max-w-3xl mx-auto">
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                Discover AI Tools & Resources
              </h1>
              <p className="text-xl text-gray-600">
                Loading...
              </p>
            </div>
          </div>
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <p className="text-lg text-gray-500">Loading browse page...</p>
          </div>
        </div>
      </div>
    }>
      <BrowsePageContent />
    </Suspense>
  );
}