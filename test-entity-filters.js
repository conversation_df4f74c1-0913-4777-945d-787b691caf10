#!/usr/bin/env node

/**
 * Simple test script to verify entity filter functionality
 * This script tests the API calls without requiring Cypress
 */

const API_BASE_URL = 'https://ai-nav.onrender.com';

async function testEntityFilters() {
  console.log('🧪 Testing Entity Filter Functionality\n');

  const testCases = [
    {
      name: 'Basic API Call (No Filters)',
      url: `${API_BASE_URL}/entities?page=1&limit=5`,
      expectSuccess: true
    },
    {
      name: 'Entity Type Filter Only',
      url: `${API_BASE_URL}/entities?page=1&limit=5&entityTypeIds=some-uuid`,
      expectSuccess: true
    },
    {
      name: 'Basic Filters (hasFreeTier)',
      url: `${API_BASE_URL}/entities?page=1&limit=5&hasFreeTier=true`,
      expectSuccess: true
    },
    {
      name: 'Entity-Specific Filter (Should NOT be sent)',
      url: `${API_BASE_URL}/entities?page=1&limit=5&technical_levels=BEGINNER`,
      expectSuccess: false,
      expectedError: 'property technical_levels should not exist'
    }
  ];

  for (const testCase of testCases) {
    console.log(`🔍 Testing: ${testCase.name}`);
    console.log(`📤 URL: ${testCase.url}`);
    
    try {
      const response = await fetch(testCase.url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const responseText = await response.text();
      
      if (response.ok) {
        if (testCase.expectSuccess) {
          console.log(`✅ Success (${response.status})`);
          try {
            const json = JSON.parse(responseText);
            console.log(`📊 Results: ${json.data?.length || 0} entities found`);
          } catch {
            console.log('📊 Response received (non-JSON)');
          }
        } else {
          console.log(`⚠️  Unexpected success (expected failure)`);
        }
      } else {
        if (!testCase.expectSuccess) {
          console.log(`✅ Expected failure (${response.status})`);
          if (testCase.expectedError && responseText.includes(testCase.expectedError)) {
            console.log(`✅ Correct error message found`);
          } else {
            console.log(`⚠️  Different error than expected`);
          }
        } else {
          console.log(`❌ Unexpected error (${response.status}):`, responseText.substring(0, 200));
        }
      }
    } catch (error) {
      console.log(`❌ Network Error:`, error.message);
    }
    
    console.log('---\n');
  }

  console.log('🎯 Test Summary:');
  console.log('- Basic API calls should work');
  console.log('- Entity-specific filters should NOT be sent to backend');
  console.log('- Frontend should handle entity-specific filters locally');
  console.log('- No 400 errors should occur for basic filtering');
}

// Run the tests
testEntityFilters().catch(console.error);
